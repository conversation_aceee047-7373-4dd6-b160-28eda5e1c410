package com.ruoyi.web.controller.merchant;

import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.domain.MerchantUser;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantUserService;
import com.ruoyi.system.service.ISysMenuService;

/**
 * 商家登录验证
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merchant")
public class MerchantLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IMerchantUserService merchantUserService;

    @Autowired
    private IMerchantService merchantService;

    /**
     * 商家登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        
        // 验证商家用户
        MerchantUser merchantUser = merchantUserService.selectMerchantUserByUserName(loginBody.getUsername());
        if (merchantUser == null)
        {
            return AjaxResult.error("用户不存在或密码错误");
        }
        
        if (!"0".equals(merchantUser.getStatus()))
        {
            return AjaxResult.error("用户已被停用，请联系管理员");
        }
        
        if (!SecurityUtils.matchesPassword(loginBody.getPassword(), merchantUser.getPassword()))
        {
            return AjaxResult.error("用户不存在或密码错误");
        }
        
        // 验证商家状态
        Merchant merchant = merchantService.selectMerchantById(merchantUser.getMerchantId());
        if (merchant == null || !"0".equals(merchant.getStatus()))
        {
            return AjaxResult.error("商家已被停用或不存在，请联系管理员");
        }
        
        // 检查商家是否过期
        if (!merchantService.isMerchantValid(merchant.getMerchantCode()))
        {
            return AjaxResult.error("商家已过期，请联系管理员续期");
        }
        
        // 生成令牌（这里需要适配商家用户的登录逻辑）
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        ajax.put("merchantId", merchant.getMerchantId());
        ajax.put("merchantName", merchant.getMerchantName());
        ajax.put("merchantCode", merchant.getMerchantCode());
        return ajax;
    }

    /**
     * 获取商家用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo()
    {
        MerchantUser user = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (user == null)
        {
            return AjaxResult.error("用户信息获取失败");
        }
        
        Merchant merchant = merchantService.selectMerchantById(user.getMerchantId());
        
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(SecurityUtils.getLoginUser().getUser());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(SecurityUtils.getLoginUser().getUser());
        
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("merchant", merchant);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取商家路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    /**
     * 商家退出登录
     */
    @PostMapping("/logout")
    public AjaxResult logout()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null)
        {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            // AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功"));
        }
        return AjaxResult.success();
    }
}
