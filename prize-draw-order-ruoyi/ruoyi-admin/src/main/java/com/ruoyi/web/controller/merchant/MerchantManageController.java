package com.ruoyi.web.controller.merchant;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.domain.MerchantConfig;
import com.ruoyi.system.domain.MerchantTable;
import com.ruoyi.system.domain.MerchantUser;
import com.ruoyi.system.service.IMerchantConfigService;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantTableService;
import com.ruoyi.system.service.IMerchantUserService;

/**
 * 商家管理后台 - 商家登录后使用
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merchant/manage")
public class MerchantManageController extends BaseController
{
    @Autowired
    private IMerchantService merchantService;

    @Autowired
    private IMerchantTableService merchantTableService;

    @Autowired
    private IMerchantConfigService merchantConfigService;

    @Autowired
    private IMerchantUserService merchantUserService;

    /**
     * 获取当前商家信息
     */
    @GetMapping("/info")
    public AjaxResult getMerchantInfo()
    {
        // 获取当前登录的商家用户
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        Merchant merchant = merchantService.selectMerchantById(currentUser.getMerchantId());
        if (merchant == null)
        {
            return error("商家信息获取失败");
        }
        
        return success(merchant);
    }

    /**
     * 更新商家微信二维码
     */
    @Log(title = "商家管理", businessType = BusinessType.UPDATE)
    @PutMapping("/wechatQrcode")
    public AjaxResult updateWechatQrcode(@RequestBody Merchant merchant)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        Merchant updateMerchant = new Merchant();
        updateMerchant.setMerchantId(currentUser.getMerchantId());
        updateMerchant.setWechatQrcode(merchant.getWechatQrcode());
        updateMerchant.setUpdateBy(getUsername());
        
        return toAjax(merchantService.updateMerchant(updateMerchant));
    }

    /**
     * 获取桌台列表
     */
    @GetMapping("/tables")
    public TableDataInfo getTables(MerchantTable merchantTable)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return getDataTable(null);
        }
        
        merchantTable.setMerchantId(currentUser.getMerchantId());
        startPage();
        List<MerchantTable> list = merchantTableService.selectMerchantTableList(merchantTable);
        return getDataTable(list);
    }

    /**
     * 新增桌台
     */
    @Log(title = "桌台管理", businessType = BusinessType.INSERT)
    @PostMapping("/table")
    public AjaxResult addTable(@Validated @RequestBody MerchantTable merchantTable)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        merchantTable.setMerchantId(currentUser.getMerchantId());
        
        if (!merchantTableService.checkTableNumberUnique(merchantTable))
        {
            return error("新增桌台'" + merchantTable.getTableNumber() + "'失败，桌台号已存在");
        }
        
        merchantTable.setCreateBy(getUsername());
        return toAjax(merchantTableService.insertMerchantTable(merchantTable));
    }

    /**
     * 修改桌台
     */
    @Log(title = "桌台管理", businessType = BusinessType.UPDATE)
    @PutMapping("/table")
    public AjaxResult editTable(@Validated @RequestBody MerchantTable merchantTable)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        // 验证桌台是否属于当前商家
        MerchantTable existingTable = merchantTableService.selectMerchantTableById(merchantTable.getTableId());
        if (existingTable == null || !existingTable.getMerchantId().equals(currentUser.getMerchantId()))
        {
            return error("桌台不存在或无权限操作");
        }
        
        if (!merchantTableService.checkTableNumberUnique(merchantTable))
        {
            return error("修改桌台'" + merchantTable.getTableNumber() + "'失败，桌台号已存在");
        }
        
        merchantTable.setUpdateBy(getUsername());
        return toAjax(merchantTableService.updateMerchantTable(merchantTable));
    }

    /**
     * 删除桌台
     */
    @Log(title = "桌台管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/table/{tableIds}")
    public AjaxResult removeTable(@PathVariable Long[] tableIds)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        // 验证所有桌台是否属于当前商家
        for (Long tableId : tableIds)
        {
            MerchantTable table = merchantTableService.selectMerchantTableById(tableId);
            if (table == null || !table.getMerchantId().equals(currentUser.getMerchantId()))
            {
                return error("桌台不存在或无权限操作");
            }
        }
        
        return toAjax(merchantTableService.deleteMerchantTableByIds(tableIds));
    }

    /**
     * 生成桌台二维码
     */
    @Log(title = "桌台管理", businessType = BusinessType.UPDATE)
    @PostMapping("/table/generateQrCode/{tableId}")
    public AjaxResult generateTableQrCode(@PathVariable Long tableId)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        // 验证桌台是否属于当前商家
        MerchantTable table = merchantTableService.selectMerchantTableById(tableId);
        if (table == null || !table.getMerchantId().equals(currentUser.getMerchantId()))
        {
            return error("桌台不存在或无权限操作");
        }
        
        String qrCode = merchantTableService.generateTableQrCode(tableId);
        if (qrCode != null)
        {
            MerchantTable updateTable = new MerchantTable();
            updateTable.setTableId(tableId);
            updateTable.setQrCode(qrCode);
            updateTable.setUpdateBy(getUsername());
            merchantTableService.updateMerchantTable(updateTable);
            return success(qrCode);
        }
        return error("二维码生成失败");
    }

    /**
     * 获取商家配置
     */
    @GetMapping("/config")
    public AjaxResult getConfig()
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        Map<String, String> configMap = merchantConfigService.selectMerchantConfigMapByMerchantId(currentUser.getMerchantId());
        return success(configMap);
    }

    /**
     * 批量保存或更新配置
     */
    @Log(title = "商家配置", businessType = BusinessType.UPDATE)
    @PostMapping("/config")
    public AjaxResult saveConfig(@RequestBody Map<String, String> configMap)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        return toAjax(merchantConfigService.batchSaveOrUpdateMerchantConfig(currentUser.getMerchantId(), configMap));
    }

    /**
     * 获取领取说明
     */
    @GetMapping("/config/claimInstruction")
    public AjaxResult getClaimInstruction()
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        String claimInstruction = merchantConfigService.selectMerchantConfigValue(
            currentUser.getMerchantId(), "claim_instruction");
        
        if (claimInstruction == null)
        {
            claimInstruction = "请到前台出示此页面领取奖品";
        }
        
        return success(claimInstruction);
    }

    /**
     * 更新领取说明
     */
    @Log(title = "商家配置", businessType = BusinessType.UPDATE)
    @PutMapping("/config/claimInstruction")
    public AjaxResult updateClaimInstruction(@RequestBody Map<String, String> params)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        String claimInstruction = params.get("claimInstruction");
        if (claimInstruction == null)
        {
            return error("领取说明不能为空");
        }
        
        MerchantConfig config = merchantConfigService.selectMerchantConfigByKey(
            currentUser.getMerchantId(), "claim_instruction");
        
        if (config == null)
        {
            config = new MerchantConfig();
            config.setMerchantId(currentUser.getMerchantId());
            config.setConfigKey("claim_instruction");
            config.setConfigValue(claimInstruction);
            config.setConfigType("text");
            config.setConfigDesc("领取说明文本");
            config.setCreateBy(getUsername());
            return toAjax(merchantConfigService.insertMerchantConfig(config));
        }
        else
        {
            config.setConfigValue(claimInstruction);
            config.setUpdateBy(getUsername());
            return toAjax(merchantConfigService.updateMerchantConfig(config));
        }
    }

    /**
     * 获取UI配置
     */
    @GetMapping("/config/ui")
    public AjaxResult getUIConfig()
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        String uiConfig = merchantConfigService.selectMerchantConfigValue(
            currentUser.getMerchantId(), "ui_config");
        
        return success(uiConfig);
    }

    /**
     * 更新UI配置
     */
    @Log(title = "商家配置", businessType = BusinessType.UPDATE)
    @PutMapping("/config/ui")
    public AjaxResult updateUIConfig(@RequestBody Map<String, String> params)
    {
        MerchantUser currentUser = merchantUserService.selectMerchantUserByUserName(SecurityUtils.getUsername());
        if (currentUser == null)
        {
            return error("用户信息获取失败");
        }
        
        String uiConfig = params.get("uiConfig");
        if (uiConfig == null)
        {
            return error("UI配置不能为空");
        }
        
        MerchantConfig config = merchantConfigService.selectMerchantConfigByKey(
            currentUser.getMerchantId(), "ui_config");
        
        if (config == null)
        {
            config = new MerchantConfig();
            config.setMerchantId(currentUser.getMerchantId());
            config.setConfigKey("ui_config");
            config.setConfigValue(uiConfig);
            config.setConfigType("json");
            config.setConfigDesc("UI配置信息");
            config.setCreateBy(getUsername());
            return toAjax(merchantConfigService.insertMerchantConfig(config));
        }
        else
        {
            config.setConfigValue(uiConfig);
            config.setUpdateBy(getUsername());
            return toAjax(merchantConfigService.updateMerchantConfig(config));
        }
    }
}
