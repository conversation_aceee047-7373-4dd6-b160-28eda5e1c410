package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.MerchantUser;
import com.ruoyi.system.service.IMerchantUserService;

/**
 * 商家用户管理 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merchant/user")
public class MerchantUserController extends BaseController
{
    @Autowired
    private IMerchantUserService merchantUserService;

    /**
     * 获取商家用户列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(MerchantUser merchantUser)
    {
        startPage();
        List<MerchantUser> list = merchantUserService.selectMerchantUserList(merchantUser);
        return getDataTable(list);
    }

    /**
     * 根据商家ID获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:list')")
    @GetMapping("/merchant/{merchantId}")
    public AjaxResult getByMerchantId(@PathVariable Long merchantId)
    {
        List<MerchantUser> list = merchantUserService.selectMerchantUsersByMerchantId(merchantId);
        return success(list);
    }

    /**
     * 导出商家用户列表
     */
    @Log(title = "商家用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('merchant:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, MerchantUser merchantUser)
    {
        List<MerchantUser> list = merchantUserService.selectMerchantUserList(merchantUser);
        ExcelUtil<MerchantUser> util = new ExcelUtil<MerchantUser>(MerchantUser.class);
        util.exportExcel(response, list, "商家用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:query')")
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        merchantUserService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        if (userId != null)
        {
            MerchantUser merchantUser = merchantUserService.selectMerchantUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, merchantUser);
        }
        return ajax;
    }

    /**
     * 新增商家用户
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:add')")
    @Log(title = "商家用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MerchantUser merchantUser)
    {
        if (!merchantUserService.checkUserNameUnique(merchantUser))
        {
            return error("新增用户'" + merchantUser.getUserName() + "'失败，登录账号已存在");
        }
        else if (!merchantUserService.checkPhoneUnique(merchantUser))
        {
            return error("新增用户'" + merchantUser.getUserName() + "'失败，手机号码已存在");
        }
        else if (!merchantUserService.checkEmailUnique(merchantUser))
        {
            return error("新增用户'" + merchantUser.getUserName() + "'失败，邮箱账号已存在");
        }
        merchantUser.setCreateBy(getUsername());
        merchantUser.setPassword(SecurityUtils.encryptPassword(merchantUser.getPassword()));
        return toAjax(merchantUserService.insertMerchantUser(merchantUser));
    }

    /**
     * 修改商家用户
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:edit')")
    @Log(title = "商家用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MerchantUser merchantUser)
    {
        merchantUserService.checkUserAllowed(merchantUser);
        merchantUserService.checkUserDataScope(merchantUser.getUserId());
        if (!merchantUserService.checkUserNameUnique(merchantUser))
        {
            return error("修改用户'" + merchantUser.getUserName() + "'失败，登录账号已存在");
        }
        else if (!merchantUserService.checkPhoneUnique(merchantUser))
        {
            return error("修改用户'" + merchantUser.getUserName() + "'失败，手机号码已存在");
        }
        else if (!merchantUserService.checkEmailUnique(merchantUser))
        {
            return error("修改用户'" + merchantUser.getUserName() + "'失败，邮箱账号已存在");
        }
        merchantUser.setUpdateBy(getUsername());
        return toAjax(merchantUserService.updateMerchantUser(merchantUser));
    }

    /**
     * 删除商家用户
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:remove')")
    @Log(title = "商家用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        if (userIds.length == 1 && SecurityUtils.getUserId().equals(userIds[0]))
        {
            return error("当前用户不能删除");
        }
        return toAjax(merchantUserService.deleteMerchantUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:resetPwd')")
    @Log(title = "商家用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody MerchantUser merchantUser)
    {
        merchantUserService.checkUserAllowed(merchantUser);
        merchantUserService.checkUserDataScope(merchantUser.getUserId());
        merchantUser.setPassword(SecurityUtils.encryptPassword(merchantUser.getPassword()));
        merchantUser.setUpdateBy(getUsername());
        return toAjax(merchantUserService.resetPwd(merchantUser));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:edit')")
    @Log(title = "商家用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MerchantUser merchantUser)
    {
        merchantUserService.checkUserAllowed(merchantUser);
        merchantUserService.checkUserDataScope(merchantUser.getUserId());
        merchantUser.setUpdateBy(getUsername());
        return toAjax(merchantUserService.updateUserStatus(merchantUser));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId)
    {
        AjaxResult ajax = AjaxResult.success();
        MerchantUser user = merchantUserService.selectMerchantUserById(userId);
        ajax.put("user", user);
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('merchant:user:edit')")
    @Log(title = "商家用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
    {
        merchantUserService.checkUserDataScope(userId);
        // 这里可以添加角色授权逻辑
        return success();
    }

    /**
     * 个人信息
     */
    @GetMapping("/profile")
    public AjaxResult profile()
    {
        Long userId = SecurityUtils.getUserId();
        MerchantUser user = merchantUserService.selectMerchantUserById(userId);
        AjaxResult ajax = AjaxResult.success(user);
        return ajax;
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/profile")
    public AjaxResult updateProfile(@RequestBody MerchantUser merchantUser)
    {
        Long userId = SecurityUtils.getUserId();
        merchantUser.setUserId(userId);
        if (!merchantUserService.checkPhoneUnique(merchantUser))
        {
            return error("修改用户'" + merchantUser.getUserName() + "'失败，手机号码已存在");
        }
        else if (!merchantUserService.checkEmailUnique(merchantUser))
        {
            return error("修改用户'" + merchantUser.getUserName() + "'失败，邮箱账号已存在");
        }
        merchantUser.setUpdateBy(getUsername());
        if (merchantUserService.updateUserProfile(merchantUser) > 0)
        {
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/profile/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword)
    {
        Long userId = SecurityUtils.getUserId();
        String userName = getUsername();
        MerchantUser user = merchantUserService.selectMerchantUserById(userId);
        if (!SecurityUtils.matchesPassword(oldPassword, user.getPassword()))
        {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, user.getPassword()))
        {
            return error("新密码不能与旧密码相同");
        }
        if (merchantUserService.resetUserPwd(userName, SecurityUtils.encryptPassword(newPassword)) > 0)
        {
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/profile/avatar")
    public AjaxResult avatar(MultipartFile file) throws Exception
    {
        if (!file.isEmpty())
        {
            String userName = getUsername();
            String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
            if (merchantUserService.updateUserAvatar(userName, avatar))
            {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", avatar);
                return ajax;
            }
        }
        return error("上传图片异常，请联系管理员");
    }



    /**
     * 下载模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<MerchantUser> util = new ExcelUtil<MerchantUser>(MerchantUser.class);
        util.importTemplateExcel(response, "商家用户数据");
    }
}
