package com.ruoyi.framework.web.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.MerchantUser;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.service.IMerchantUserService;
import com.ruoyi.system.service.IMerchantService;
import java.util.HashSet;
import java.util.Set;

/**
 * 商家用户验证处理
 *
 * <AUTHOR>
 */
@Service("merchantUserDetailsService")
public class MerchantUserDetailsService implements UserDetailsService
{
    private static final Logger log = LoggerFactory.getLogger(MerchantUserDetailsService.class);

    @Autowired
    private IMerchantUserService merchantUserService;

    @Autowired
    private IMerchantService merchantService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException
    {
        MerchantUser merchantUser = merchantUserService.selectMerchantUserByUserName(username);
        if (StringUtils.isNull(merchantUser))
        {
            log.info("登录商家用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        }
        else if ("2".equals(merchantUser.getDelFlag()))
        {
            log.info("登录商家用户：{} 已被删除.", username);
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        }
        else if (!"0".equals(merchantUser.getStatus()))
        {
            log.info("登录商家用户：{} 已被停用.", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }

        // 验证商家状态
        Merchant merchant = merchantService.selectMerchantById(merchantUser.getMerchantId());
        if (merchant == null || !"0".equals(merchant.getStatus()))
        {
            log.info("商家用户：{} 所属商家已被停用或不存在.", username);
            throw new ServiceException("商家已被停用或不存在，请联系管理员");
        }

        // 检查商家是否过期
        if (!merchantService.isMerchantValid(merchant.getMerchantCode()))
        {
            log.info("商家用户：{} 所属商家已过期.", username);
            throw new ServiceException("商家已过期，请联系管理员续期");
        }

        return createMerchantLoginUser(merchantUser, merchant);
    }

    /**
     * 创建商家登录用户
     * 
     * @param merchantUser 商家用户
     * @param merchant 商家信息
     * @return 登录用户
     */
    public UserDetails createMerchantLoginUser(MerchantUser merchantUser, Merchant merchant)
    {
        // 为商家用户创建基本权限集合
        Set<String> permissions = new HashSet<>();
        permissions.add("merchant:basic:view");
        permissions.add("merchant:order:view");
        permissions.add("merchant:prize:view");
        
        // 创建一个适配的SysUser对象用于LoginUser
        com.ruoyi.common.core.domain.entity.SysUser adaptedUser = new com.ruoyi.common.core.domain.entity.SysUser();
        adaptedUser.setUserId(merchantUser.getUserId());
        adaptedUser.setUserName(merchantUser.getUserName());
        adaptedUser.setNickName(merchantUser.getNickName());
        adaptedUser.setEmail(merchantUser.getEmail());
        adaptedUser.setPhonenumber(merchantUser.getPhonenumber());
        adaptedUser.setSex(merchantUser.getSex());
        adaptedUser.setAvatar(merchantUser.getAvatar());
        adaptedUser.setPassword(merchantUser.getPassword());
        adaptedUser.setStatus(merchantUser.getStatus());
        adaptedUser.setDelFlag(merchantUser.getDelFlag());
        adaptedUser.setLoginIp(merchantUser.getLoginIp());
        adaptedUser.setLoginDate(merchantUser.getLoginDate());
        adaptedUser.setCreateBy(merchantUser.getCreateBy());
        adaptedUser.setCreateTime(merchantUser.getCreateTime());
        adaptedUser.setUpdateBy(merchantUser.getUpdateBy());
        adaptedUser.setUpdateTime(merchantUser.getUpdateTime());
        adaptedUser.setRemark(merchantUser.getRemark());
        
        // 设置商家相关信息到remark字段，用于后续识别
        adaptedUser.setRemark("MERCHANT_USER:" + merchant.getMerchantId() + ":" + merchant.getMerchantCode());
        
        return new LoginUser(merchantUser.getUserId(), merchant.getMerchantId(), adaptedUser, permissions);
    }
}
