package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.domain.MerchantUser;
import com.ruoyi.system.mapper.MerchantUserMapper;
import com.ruoyi.system.service.IMerchantUserService;

/**
 * 商家用户管理 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class MerchantUserServiceImpl implements IMerchantUserService
{
    private static final Logger log = LoggerFactory.getLogger(MerchantUserServiceImpl.class);

    @Autowired
    private MerchantUserMapper merchantUserMapper;

    /**
     * 查询商家用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public MerchantUser selectMerchantUserById(Long userId)
    {
        return merchantUserMapper.selectMerchantUserById(userId);
    }

    /**
     * 根据用户名查询商家用户信息
     * 
     * @param userName 用户名
     * @return 用户信息
     */
    @Override
    public MerchantUser selectMerchantUserByUserName(String userName)
    {
        return merchantUserMapper.selectMerchantUserByUserName(userName);
    }

    /**
     * 查询商家用户列表
     * 
     * @param merchantUser 用户信息
     * @return 用户集合
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public List<MerchantUser> selectMerchantUserList(MerchantUser merchantUser)
    {
        return merchantUserMapper.selectMerchantUserList(merchantUser);
    }

    /**
     * 根据商家ID查询用户列表
     * 
     * @param merchantId 商家ID
     * @return 用户集合
     */
    @Override
    public List<MerchantUser> selectMerchantUsersByMerchantId(Long merchantId)
    {
        return merchantUserMapper.selectMerchantUsersByMerchantId(merchantId);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(MerchantUser merchantUser)
    {
        Long userId = StringUtils.isNull(merchantUser.getUserId()) ? -1L : merchantUser.getUserId();
        MerchantUser info = merchantUserMapper.checkUserNameUnique(merchantUser.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkPhoneUnique(MerchantUser merchantUser)
    {
        Long userId = StringUtils.isNull(merchantUser.getUserId()) ? -1L : merchantUser.getUserId();
        MerchantUser info = merchantUserMapper.checkPhoneUnique(merchantUser.getPhonenumber());
        if (com.ruoyi.common.utils.StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkEmailUnique(MerchantUser merchantUser)
    {
        Long userId = StringUtils.isNull(merchantUser.getUserId()) ? -1L : merchantUser.getUserId();
        MerchantUser info = merchantUserMapper.checkEmailUnique(merchantUser.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param merchantUser 用户信息
     */
    @Override
    public void checkUserAllowed(MerchantUser merchantUser)
    {
        if (StringUtils.isNotNull(merchantUser.getUserId()) && merchantUser.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            MerchantUser merchantUser = new MerchantUser();
            merchantUser.setUserId(userId);
            List<MerchantUser> users = this.selectMerchantUserList(merchantUser);
            if (StringUtils.isEmpty(users))
            {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增商家用户
     * 
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMerchantUser(MerchantUser merchantUser)
    {
        merchantUser.setCreateTime(DateUtils.getNowDate());
        return merchantUserMapper.insertMerchantUser(merchantUser);
    }

    /**
     * 注册商家用户信息
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    public boolean registerMerchantUser(MerchantUser merchantUser)
    {
        merchantUser.setCreateTime(DateUtils.getNowDate());
        return merchantUserMapper.insertMerchantUser(merchantUser) > 0;
    }

    /**
     * 修改商家用户
     * 
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMerchantUser(MerchantUser merchantUser)
    {
        merchantUser.setUpdateTime(DateUtils.getNowDate());
        return merchantUserMapper.updateMerchantUser(merchantUser);
    }

    /**
     * 修改用户状态
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(MerchantUser merchantUser)
    {
        return merchantUserMapper.updateMerchantUser(merchantUser);
    }

    /**
     * 修改用户基本信息
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(MerchantUser merchantUser)
    {
        return merchantUserMapper.updateMerchantUser(merchantUser);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        return merchantUserMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(MerchantUser merchantUser)
    {
        return merchantUserMapper.updateMerchantUser(merchantUser);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password)
    {
        return merchantUserMapper.resetUserPwd(userName, password);
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMerchantUserById(Long userId)
    {
        return merchantUserMapper.deleteMerchantUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMerchantUserByIds(Long[] userIds)
    {
        for (Long userId : userIds)
        {
            checkUserAllowed(new MerchantUser(userId));
            checkUserDataScope(userId);
        }
        return merchantUserMapper.deleteMerchantUserByIds(userIds);
    }

    /**
     * 根据商家ID删除用户
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    @Override
    public int deleteMerchantUserByMerchantId(Long merchantId)
    {
        return merchantUserMapper.deleteMerchantUserByMerchantId(merchantId);
    }

    /**
     * 统计商家的用户数量
     * 
     * @param merchantId 商家ID
     * @return 用户数量
     */
    @Override
    public int countUsersByMerchantId(Long merchantId)
    {
        return merchantUserMapper.countUsersByMerchantId(merchantId);
    }


}
