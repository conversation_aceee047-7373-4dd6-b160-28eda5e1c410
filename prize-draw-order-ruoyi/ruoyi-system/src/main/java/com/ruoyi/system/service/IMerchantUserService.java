package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.MerchantUser;

/**
 * 商家用户管理 服务层
 * 
 * <AUTHOR>
 */
public interface IMerchantUserService
{
    /**
     * 查询商家用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    public MerchantUser selectMerchantUserById(Long userId);

    /**
     * 根据用户名查询商家用户信息
     * 
     * @param userName 用户名
     * @return 用户信息
     */
    public MerchantUser selectMerchantUserByUserName(String userName);

    /**
     * 查询商家用户列表
     * 
     * @param merchantUser 用户信息
     * @return 用户集合
     */
    public List<MerchantUser> selectMerchantUserList(MerchantUser merchantUser);

    /**
     * 根据商家ID查询用户列表
     * 
     * @param merchantId 商家ID
     * @return 用户集合
     */
    public List<MerchantUser> selectMerchantUsersByMerchantId(Long merchantId);

    /**
     * 校验用户名称是否唯一
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(MerchantUser merchantUser);

    /**
     * 校验手机号码是否唯一
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(MerchantUser merchantUser);

    /**
     * 校验email是否唯一
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    public boolean checkEmailUnique(MerchantUser merchantUser);

    /**
     * 校验用户是否允许操作
     *
     * @param merchantUser 用户信息
     */
    public void checkUserAllowed(MerchantUser merchantUser);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId);

    /**
     * 新增商家用户
     * 
     * @param merchantUser 用户信息
     * @return 结果
     */
    public int insertMerchantUser(MerchantUser merchantUser);

    /**
     * 注册商家用户信息
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    public boolean registerMerchantUser(MerchantUser merchantUser);

    /**
     * 修改商家用户
     * 
     * @param merchantUser 用户信息
     * @return 结果
     */
    public int updateMerchantUser(MerchantUser merchantUser);

    /**
     * 修改用户状态
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    public int updateUserStatus(MerchantUser merchantUser);

    /**
     * 修改用户基本信息
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    public int updateUserProfile(MerchantUser merchantUser);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param merchantUser 用户信息
     * @return 结果
     */
    public int resetPwd(MerchantUser merchantUser);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteMerchantUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteMerchantUserByIds(Long[] userIds);

    /**
     * 根据商家ID删除用户
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    public int deleteMerchantUserByMerchantId(Long merchantId);

    /**
     * 统计商家的用户数量
     * 
     * @param merchantId 商家ID
     * @return 用户数量
     */
    public int countUsersByMerchantId(Long merchantId);


}
