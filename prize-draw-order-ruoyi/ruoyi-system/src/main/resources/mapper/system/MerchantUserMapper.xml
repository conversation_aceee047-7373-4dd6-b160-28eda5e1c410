<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MerchantUserMapper">
    
    <resultMap type="MerchantUser" id="MerchantUserResult">
        <id     property="userId"        column="user_id"        />
        <result property="merchantId"    column="merchant_id"    />
        <result property="userName"      column="user_name"      />
        <result property="nickName"      column="nick_name"      />
        <result property="email"         column="email"          />
        <result property="phonenumber"   column="phonenumber"    />
        <result property="sex"           column="sex"            />
        <result property="avatar"        column="avatar"         />
        <result property="password"      column="password"       />
        <result property="status"        column="status"         />
        <result property="delFlag"       column="del_flag"       />
        <result property="loginIp"       column="login_ip"       />
        <result property="loginDate"     column="login_date"     />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
        <result property="merchantName"  column="merchant_name"  />
    </resultMap>
    
    <sql id="selectMerchantUserVo">
        select u.user_id, u.merchant_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, 
               u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, 
               u.create_time, u.remark, m.merchant_name
        from merchant_user u
        left join merchant m on u.merchant_id = m.merchant_id
    </sql>
    
    <select id="selectMerchantUserList" parameterType="MerchantUser" resultMap="MerchantUserResult">
        <include refid="selectMerchantUserVo"/>
        where u.del_flag = '0'
        <if test="merchantId != null and merchantId != 0">
            AND u.merchant_id = #{merchantId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        order by u.create_time desc
    </select>
    
    <select id="selectMerchantUserByUserName" parameterType="String" resultMap="MerchantUserResult">
        <include refid="selectMerchantUserVo"/>
        where u.user_name = #{userName} and u.del_flag = '0'
    </select>
    
    <select id="selectMerchantUserById" parameterType="Long" resultMap="MerchantUserResult">
        <include refid="selectMerchantUserVo"/>
        where u.user_id = #{userId}
    </select>
    
    <select id="selectMerchantUsersByMerchantId" parameterType="Long" resultMap="MerchantUserResult">
        <include refid="selectMerchantUserVo"/>
        where u.merchant_id = #{merchantId} and u.del_flag = '0'
        order by u.create_time desc
    </select>
    
    <select id="checkUserNameUnique" parameterType="String" resultMap="MerchantUserResult">
        select user_id, user_name from merchant_user where user_name = #{userName} and del_flag = '0' limit 1
    </select>
    
    <select id="checkPhoneUnique" parameterType="String" resultMap="MerchantUserResult">
        select user_id, phonenumber from merchant_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
    </select>
    
    <select id="checkEmailUnique" parameterType="String" resultMap="MerchantUserResult">
        select user_id, email from merchant_user where email = #{email} and del_flag = '0' limit 1
    </select>
    
    <insert id="insertMerchantUser" parameterType="MerchantUser" useGeneratedKeys="true" keyProperty="userId">
        insert into merchant_user(
            <if test="userId != null and userId != 0">user_id,</if>
            <if test="merchantId != null and merchantId != 0">merchant_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
            <if test="sex != null and sex != ''">sex,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time
        )values(
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="merchantId != null and merchantId != ''">#{merchantId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
            <if test="sex != null and sex != ''">#{sex},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate()
        )
    </insert>
    
    <update id="updateMerchantUser" parameterType="MerchantUser">
        update merchant_user
        <set>
            <if test="merchantId != null and merchantId != 0">merchant_id = #{merchantId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>
    
    <update id="updateUserAvatar" parameterType="String">
        update merchant_user set avatar = #{avatar} where user_name = #{userName}
    </update>
    
    <update id="resetUserPwd" parameterType="String">
        update merchant_user set password = #{password} where user_name = #{userName}
    </update>
    
    <delete id="deleteMerchantUserById" parameterType="Long">
        update merchant_user set del_flag = '2' where user_id = #{userId}
    </delete>
    
    <delete id="deleteMerchantUserByIds" parameterType="String">
        update merchant_user set del_flag = '2' where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
    
    <delete id="deleteMerchantUserByMerchantId" parameterType="Long">
        update merchant_user set del_flag = '2' where merchant_id = #{merchantId}
    </delete>
    
    <select id="countUsersByMerchantId" parameterType="Long" resultType="int">
        select count(*) from merchant_user where merchant_id = #{merchantId} and del_flag = '0'
    </select>
    
</mapper>
