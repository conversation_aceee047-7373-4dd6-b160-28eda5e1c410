<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d386e267-a9aa-4c97-9a07-e1b05704ddf6" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/prize-draw-order-ruoyi/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-admin/src/main/resources/application-druid.yml" beforeDir="false" afterPath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-admin/src/main/resources/application-druid.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-admin/src/main/resources/logback.xml" beforeDir="false" afterPath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-admin/src/main/resources/logback.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-quartz/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/prize-draw-order-ruoyi/ruoyi-quartz/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/prize-draw-order-uniapp/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/prize-draw-order-uniapp/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/prize-draw-order-uniapp/pages/index/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/prize-draw-order-uniapp/pages/index/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="30b4Xqwp4hi55qAWCMrFW4rgUsy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.RuoYiApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "/Users/<USER>/projects/prize-draw-order",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "/Applications/IntelliJ IDEA.app/Contents/plugins/javascript-plugin/jsLanguageServicesImpl/external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="d386e267-a9aa-4c97-9a07-e1b05704ddf6" name="更改" comment="" />
      <created>1753882980458</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753882980458</updated>
      <workItem from="1753882981524" duration="2312000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>